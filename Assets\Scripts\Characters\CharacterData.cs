using UnityEngine;

namespace TacticalCombatSystem.Characters
{
    /// <summary>
    /// Contains the base data for a character
    /// </summary>
    [CreateAssetMenu(fileName = "New Character Data", menuName = "Tactical Combat System/Character Data")]
    public class CharacterData : ScriptableObject
    {
        [Header("Basic Info")]
        [SerializeField] private string characterName = "New Character";
        [SerializeField] private string description = "Character description";
        [SerializeField] private Sprite portrait;
        
        [Header("Base Stats")]
        [SerializeField] private int baseMaxHP = 100;
        [SerializeField] private int baseMaxMP = 50;
        [SerializeField] private int baseAttack = 10;
        [SerializeField] private int baseDefense = 5;
        [SerializeField] private int baseMagicAttack = 8;
        [SerializeField] private int baseMagicDefense = 4;
        [SerializeField] private int baseSpeed = 5;
        
        [Header("Visuals")]
        [SerializeField] private GameObject characterPrefab;
        [SerializeField] private RuntimeAnimatorController animatorController;
        
        public bool IsPlayable; // Add this line
        
        // Properties
        public string CharacterName => characterName;
        public string Description => description;
        public Sprite Portrait => portrait;
        public int BaseMaxHP => baseMaxHP;
        public int BaseMaxMP => baseMaxMP;
        public int BaseAttack => baseAttack;
        public int BaseDefense => baseDefense;
        public int BaseMagicAttack => baseMagicAttack;
        public int BaseMagicDefense => baseMagicDefense;
        public int BaseSpeed => baseSpeed;
        public GameObject CharacterPrefab => characterPrefab;
        public RuntimeAnimatorController AnimatorController => animatorController;
        
        /// <summary>
        /// Create a copy of this character data
        /// </summary>
        public CharacterData Clone()
        {
            return Instantiate(this);
        }
    }
}
