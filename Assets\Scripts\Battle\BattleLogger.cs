using UnityEngine;
using System;
using TacticalCombatSystem.Interfaces;
using UnityEngine.UI;
using TacticalCombatSystem.UI;

public static class BattleLogger
{
    public static void Log(string message)
    {
        string formattedMessage = $"[{DateTime.Now.ToString("HH:mm:ss")}] {message}";
        Debug.Log(formattedMessage);
        
        // Send to BattleUI if available
        var battleUI = UnityEngine.Object.FindFirstObjectByType<BattleUI>();
        if (battleUI != null)
        {
            battleUI.AddLogMessage(formattedMessage);
        }
    }

    public static void LogAction(ICombatParticipant actor, string action, ICombatParticipant target = null)
    {
        string targetInfo = target != null ? $" on {target.Name}" : "";
        Log($"{actor.Name} used {action}{targetInfo}");
    }

    public static void LogDamage(ICombatParticipant target, int damage)
    {
        Log($"{target.Name} took {damage} damage");
    }

    public static void LogStatus(ICombatParticipant target, string status, bool applied)
    {
        Log($"{target.Name} {(applied ? "gained" : "lost")} {status} status");
    }
}
