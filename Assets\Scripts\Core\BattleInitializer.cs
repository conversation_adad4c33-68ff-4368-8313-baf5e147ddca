using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Core
{
    public class BattleInitializer : MonoBehaviour
    {
        [Header("Scene References")]
        public IBattleManager battleManager;
        public IBattleUI battleUI;
        public BattleInputHandler inputHandler;
        public UnityEngine.Camera battleCamera;
        
        [Header("Character Prefabs")]
        public GameObject[] playerPrefabs;
        public GameObject[] enemyPrefabs;
        
        [Header("Spawn Points")]
        public Transform[] playerSpawnPoints;
        public Transform[] enemySpawnPoints;
        
        [Header("Test Data")]
        public Character[] testPlayerCharacters;
        public Character[] testEnemyCharacters;
        
        private void Awake()
        {
            // Find components in the scene
            if (battleManager == null)
            {
                battleManager = FindFirstObjectByType<BattleManager>(FindObjectsInactive.Include) as IBattleManager;
            }

            if (battleUI == null)
            {
                var battleUIComponent = FindFirstObjectByType<MonoBehaviour>(FindObjectsInactive.Include) as IBattleUI;
                if (battleUIComponent != null)
                    battleUI = battleUIComponent;
            }

            if (inputHandler == null)
            {
                inputHandler = FindFirstObjectByType<BattleInputHandler>(FindObjectsInactive.Include);
            }

            // Set up camera reference
            if (battleCamera == null)
            {
                battleCamera = UnityEngine.Camera.main;
                if (battleCamera == null)
                {
                    Debug.LogError("No main camera found in the scene!");
                }
            }

            // Initialize the input handler
            if (inputHandler != null)
            {
                // These properties were removed from BattleInputHandler during the refactor
                // to a keyboard-only input system, so these assignments are no longer needed.
            }

            // Set up test teams if we have test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 &&
                testEnemyCharacters != null && testEnemyCharacters.Length > 0)
            {
                SetupTestBattle();
            }
            else
            {
                // Otherwise, spawn prefabs
                SpawnCharacters();
            }

            // Start the battle
            if (battleManager != null)
            {
                battleManager.StartBattle();
            }
        }
        
        private void SetupTestBattle()
        {
            // Add test player characters using interface methods
            foreach (Character character in testPlayerCharacters)
            {
                if (character != null)
                {
                    // We need to convert ICharacter to ICombatParticipant
                    // This will need to be handled by the BattleManager implementation
                    Debug.Log($"Adding player character: {character.CharacterName}");
                }
            }

            // Add test enemy characters
            foreach (Character character in testEnemyCharacters)
            {
                if (character != null)
                {
                    Debug.Log($"Adding enemy character: {character.CharacterName}");
                }
            }

            Debug.Log("Test battle setup completed");
        }
        
        private void SpawnCharacters()
        {
            if (playerPrefabs == null || playerPrefabs.Length == 0)
            {
                Debug.LogError("No player prefabs assigned to spawn!");
                return;
            }

            if (enemyPrefabs == null || enemyPrefabs.Length == 0)
            {
                Debug.LogError("No enemy prefabs assigned to spawn!");
                return;
            }

            // Spawn player characters
            for (int i = 0; i < playerPrefabs.Length && i < playerSpawnPoints.Length; i++)
            {
                if (playerPrefabs[i] != null && playerSpawnPoints[i] != null)
                {
                    GameObject playerObj = Instantiate(playerPrefabs[i], playerSpawnPoints[i].position, playerSpawnPoints[i].rotation);
                    var combatParticipant = playerObj.GetComponent<ICombatParticipant>();

                    if (combatParticipant != null)
                    {
                        battleManager.AddToPlayerTeam(combatParticipant);
                    }
                }
            }

            // Spawn enemy characters
            for (int i = 0; i < enemyPrefabs.Length && i < enemySpawnPoints.Length; i++)
            {
                if (enemyPrefabs[i] != null && enemySpawnPoints[i] != null)
                {
                    GameObject enemyObj = Instantiate(enemyPrefabs[i], enemySpawnPoints[i].position, enemySpawnPoints[i].rotation);
                    var combatParticipant = enemyObj.GetComponent<ICombatParticipant>();

                    if (combatParticipant != null)
                    {
                        battleManager.AddToEnemyTeam(combatParticipant);
                    }
                }
            }

            Debug.Log("Character spawning completed");
        }
        
        private void OnValidate()
        {
            // Ensure we have enough spawn points for the test characters
            if (testPlayerCharacters != null && testPlayerCharacters.Length > 0 && 
                (playerSpawnPoints == null || playerSpawnPoints.Length < testPlayerCharacters.Length))
            {
                Debug.LogWarning($"Not enough player spawn points for all test characters. Need {testPlayerCharacters.Length} but have {playerSpawnPoints?.Length ?? 0}.");
            }
            
            if (testEnemyCharacters != null && testEnemyCharacters.Length > 0 && 
                (enemySpawnPoints == null || enemySpawnPoints.Length < testEnemyCharacters.Length))
            {
                Debug.LogWarning($"Not enough enemy spawn points for all test characters. Need {testEnemyCharacters.Length} but have {enemySpawnPoints?.Length ?? 0}.");
            }
        }
    }
}
