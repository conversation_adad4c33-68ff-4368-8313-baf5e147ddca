%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4fce694c145870a499e3db7da3479392, type: 3}
  m_Name: PlayerCharacter
  m_EditorClassIdentifier: Assembly-CSharp::TacticalCombatSystem.Characters.Character
  characterName: PlayerHero
  description: Character description
  portrait: {fileID: 21300090, guid: 1eaee135ce037439d925cee5e41ce026, type: 3}
  characterPrefab: {fileID: 85698199387632563, guid: d12787846cb4f5245a073837b96fea9a, type: 3}
  maxHealth: 100
  maxMana: 50
  attack: 15
  defense: 10
  magicAttack: 5
  magicDefense: 5
  speed: 12
  luck: 10
  criticalChance: 0.1
  criticalMultiplier: 1.5
  level: 1
  currentExp: 0
  expToNextLevel: 100
  startingAbilities: []
  abilities:
  - {fileID: 11400000, guid: 8a7caa6a70a37db41ab2a66da10f366b, type: 2}
  battleVisualPrefab: {fileID: 6766536238432138802, guid: 29275473dc42c1a499187925872e54cb, type: 3}
  animatorController: {fileID: 9100000, guid: 035023331dbb59f40832fd4d54e4957e, type: 2}
  startingStatusEffects: []
  isPlayable: 1
