using UnityEngine;
using System.Collections.Generic;

[CreateAssetMenu(fileName = "PrefabSetupConfig", menuName = "Tactical Combat/Prefab Setup Config")]
public class PrefabSetupConfig : ScriptableObject
{
    [Header("File Paths")]
    public string prefabPath = "Assets/Prefabs/Characters";
    public string prefabSavePath = "Assets/Prefabs/Characters";
    public string uiPrefabPath = "Assets/Prefabs/UI";
    public string materialsPath = "Assets/Materials";
    public string prefabNamePrefix = "Character_";

    [Header("Character Settings")]
    public string defaultCharacterName = "NewCharacter";
    public string defaultPrefabName = "NewCharacter";
    public string defaultMaterialName = "CharacterMaterial";
    public Color defaultCharacterColor = Color.white;
    public Vector3 colliderSize = new Vector3(1, 2, 1);
    public Vector3 colliderCenter = new Vector3(0, 1, 0);
    public float defaultHealth = 100f;
    public float defaultDamage = 10f;
    public float defaultMoveSpeed = 5f;
    public float defaultAttackRange = 2f;
    public float defaultAttackRate = 1f;

    [Header("Visual Settings")]
    public Material defaultMaterial;
    public Vector3 characterScale = Vector3.one;
    public Vector3 uiOffset = new Vector3(0, 2f, 0);

    [Header("UI Settings")]
    public Vector2 canvasSize = new Vector2(200, 100);
    public Vector3 canvasPosition = new Vector3(0, 2f, 0);
    public Vector3 canvasScale = new Vector3(0.01f, 0.01f, 0.01f);
    public float canvasPixelsPerUnit = 1f;
    public float canvasReferencePixelsPerUnit = 100f;
    
    // Health Bar
    public Color healthBarFillColor = Color.red;
    public Color healthBarBackgroundColor = new Color(0.2f, 0.2f, 0.2f, 0.7f);
    
    // Name Text
    public int nameFontSize = 14;
    public Color nameTextColor = Color.white;

    [Header("Damage Number Settings")]
    public Color damageNumberColor = new Color(1f, 0.2f, 0.2f, 1f);
    public int damageNumberFontSize = 24;
    public Vector2 damageNumberSize = new Vector2(100, 50);

    [Header("Required Components")]
    public List<string> requiredComponents = new List<string>
    {
        "TacticalCombatSystem.CharacterVisual",
        "UnityEngine.BoxCollider",
        "UnityEngine.Rigidbody"
    };

    [Header("Validation")]
    public bool validatePrefabOnCreate = true;
    public bool showProgressBars = true;
    public bool enableUndo = true;
}
