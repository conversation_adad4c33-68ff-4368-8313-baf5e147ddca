%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4fce694c145870a499e3db7da3479392, type: 3}
  m_Name: EnemyGrunt
  m_EditorClassIdentifier: Assembly-CSharp::TacticalCombatSystem.Characters.Character
  characterName: EnemyGrunt
  description: Character description
  portrait: {fileID: 21300000, guid: 7e73e4be78a578649bd6c28e037ef6fa, type: 3}
  characterPrefab: {fileID: 2333246135476635899, guid: bae3b6da9f558a04994badd5424cbd6f, type: 3}
  maxHealth: 50
  maxMana: 20
  attack: 8
  defense: 5
  magicAttack: 5
  magicDefense: 5
  speed: 8
  luck: 10
  criticalChance: 0.1
  criticalMultiplier: 1.5
  level: 1
  currentExp: 0
  expToNextLevel: 100
  startingAbilities: []
  abilities: []
  battleVisualPrefab: {fileID: 2333246135476635899, guid: bae3b6da9f558a04994badd5424cbd6f, type: 3}
  animatorController: {fileID: 9100000, guid: 035023331dbb59f40832fd4d54e4957e, type: 2}
  startingStatusEffects: []
