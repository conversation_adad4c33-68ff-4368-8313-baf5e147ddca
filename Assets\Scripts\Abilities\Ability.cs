using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Abilities
{
    [CreateAssetMenu(fileName = "New Ability", menuName = "Tactical Combat/Ability")]
    public class Ability : CombatAction
    {
        [Header("Ability Settings")]
        [Tooltip("The specific targeting for this ability.")]
        [SerializeField] private TargetType _targetType = TargetType.SingleEnemy;

        [<PERSON><PERSON>("Animation")]
        [Tooltip("The name of the animation trigger to play when this action is executed.")]
        [SerializeField] private string _animationTrigger = "Skill";

        [Header("Effects")]
        [SerializeField] private List<AbilityEffect> effects = new List<AbilityEffect>();

        // --- Overrides from CombatAction ---
        public override string AnimationTriggerName => _animationTrigger;
        public override TargetType TargetType => _targetType;

        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            Debug.Log($"{user.ParticipantName} uses {actionName} on {target.ParticipantName}!");

            // Pay costs
            user.CurrentMP -= mpCost;
            user.CurrentHP = Mathf.Max(1, user.CurrentHP - hpCost);

            // Apply effects
            foreach (var effect in effects)
            {
                effect.Apply(user, target);
            }

            // Apply cooldown
            ApplyCooldown();
        }
    }
}
