using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Actions
{
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Defend")]
    public class DefendAction : CombatAction
    {
        public override string AnimationTriggerName => "Defend";

        public override TargetType TargetType => TargetType.Self;

        private void OnEnable()
        {
            actionName = "Defend";
            description = "Reduces incoming damage by 50% until your next turn.";
            validTargets = TargetTeam.Self;
            mpCost = 0;
        }

        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            // Create a simple status effect to act as a flag
            var defendEffect = StatusEffect.CreateInstance<StatusEffect>();
            defendEffect.name = "Defending";
            defendEffect.statusName = "Defending";
            defendEffect.description = "Incoming damage is halved.";
            defendEffect.duration = 2; // Lasts until the end of the user's next turn
            defendEffect.statusType = StatusType.Buff;

            user.ApplyStatusEffect(defendEffect);

            Debug.Log($"{user.ParticipantName} is defending!");
        }
    }
}
