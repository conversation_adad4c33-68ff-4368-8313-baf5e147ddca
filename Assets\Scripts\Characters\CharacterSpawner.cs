using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;

namespace TacticalCombatSystem.Characters
{
    public class CharacterSpawner : MonoBehaviour
    {
        [System.Serializable]
        public class SpawnPoint
        {
            public Transform transform;
            public bool isPlayerTeam;
        }

        [Header("References")]
        public GameObject characterPrefab;
        public List<SpawnPoint> spawnPoints = new List<SpawnPoint>();
        
        [Header("Test Data")]
        public List<Character> testPlayerTeam = new List<Character>();
        public List<Character> testEnemyTeam = new List<Character>();

        private void Start()
        {
            SpawnTeams();
        }
        
        public void SpawnTeams()
        {
            if (characterPrefab == null)
            {
                Debug.LogError("Character prefab is not assigned!");
                return;
            }

            var battleManager = FindFirstObjectByType<MonoBehaviour>() as IBattleManager;
            if (battleManager == null)
            {
                Debug.LogError("BattleManager not found in the scene!");
                return;
            }

            // Clear existing characters
            foreach (var spawnPoint in spawnPoints)
            {
                if (spawnPoint.transform.childCount > 0)
                {
                    for (int i = spawnPoint.transform.childCount - 1; i >= 0; i--)
                    {
                        Destroy(spawnPoint.transform.GetChild(i).gameObject);
                    }
                }
            }

            // Spawn player team from test data
            for (int i = 0; i < testPlayerTeam.Count; i++)
            {
                var spawnPoint = GetSpawnPoint(true, i);
                if (spawnPoint != null && testPlayerTeam[i] != null)
                {
                    var combatParticipant = SpawnCharacter(testPlayerTeam[i], spawnPoint, true);
                    if (combatParticipant != null)
                    {
                        battleManager.AddToPlayerTeam(combatParticipant);
                    }
                }
            }

            // Spawn enemy team from test data
            for (int i = 0; i < testEnemyTeam.Count; i++)
            {
                var spawnPoint = GetSpawnPoint(false, i);
                if (spawnPoint != null && testEnemyTeam[i] != null)
                {
                    var combatParticipant = SpawnCharacter(testEnemyTeam[i], spawnPoint, false);
                    if (combatParticipant != null)
                    {
                        battleManager.AddToEnemyTeam(combatParticipant);
                    }
                }
            }
        }
        
        private Transform GetSpawnPoint(bool isPlayerTeam, int index)
        {
            var teamSpawnPoints = spawnPoints.FindAll(p => p.isPlayerTeam == isPlayerTeam);
            if (teamSpawnPoints.Count == 0)
            {
                Debug.LogError($"No spawn points found for { (isPlayerTeam ? "player" : "enemy") } team!");
                return null;
            }
            
            // Use modulo to loop through available spawn points if there are more characters than spawn points
            return teamSpawnPoints[Mathf.Min(index, teamSpawnPoints.Count - 1)].transform;
        }
        
        private ICombatParticipant SpawnCharacter(Character characterData, Transform spawnPoint, bool isPlayerTeam)
        {
            if (characterData == null || spawnPoint == null) return null;

            // Use the Character's CreateInstance method to create a proper BaseCharacter
            var baseCharacter = characterData.CreateInstance(spawnPoint, isPlayerTeam);
            if (baseCharacter == null) return null;

            // Position the character at the spawn point
            baseCharacter.transform.position = spawnPoint.position;
            baseCharacter.transform.rotation = spawnPoint.rotation;
            baseCharacter.name = $"Character_{characterData.CharacterName}";

            // Set up world space UI
            var canvas = baseCharacter.GetComponentInChildren<Canvas>();
            if (canvas != null)
            {
                // Make UI face the camera
                canvas.worldCamera = UnityEngine.Camera.main;

                // For world space UI, we might want to make it face the camera at all times
                var billboard = canvas.GetComponent<Billboard>();
                if (billboard == null)
                {
                    billboard = canvas.gameObject.AddComponent<Billboard>();
                    billboard.Camera = UnityEngine.Camera.main;
                }
            }

            return baseCharacter;
        }
        
        // Helper method to spawn a single character for testing
        public ICombatParticipant SpawnTestCharacter(Character characterData, bool isPlayerTeam, int spawnIndex = 0)
        {
            var spawnPoint = GetSpawnPoint(isPlayerTeam, spawnIndex);
            if (spawnPoint != null)
            {
                return SpawnCharacter(characterData, spawnPoint, isPlayerTeam);
            }
            return null;
        }
    }
    
    // Simple billboard script to make UI face the camera
    public class Billboard : MonoBehaviour
    {
        public UnityEngine.Camera Camera;
        public bool FreezeXZ = true;
        
        private void Start()
        {
            if (Camera == null)
            {
                Camera = UnityEngine.Camera.main;
            }
        }
        
        private void LateUpdate()
        {
            if (Camera == null) return;
            
            Vector3 lookDirection = Camera.transform.forward;
            if (FreezeXZ)
            {
                lookDirection.y = 0;
                lookDirection.Normalize();
                lookDirection = Quaternion.LookRotation(Vector3.forward, Vector3.up) * lookDirection;
            }
            
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }
    }
}
