using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;
using UnityEngine.SceneManagement;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Battle;

public class SceneSetupHelper : EditorWindow
{
    [MenuItem("Tactical Combat/Setup Battle Scene")]
    public static void SetupBattleScene()
    {
        // Create a new scene or get the active one
        Scene scene = EditorSceneManager.GetActiveScene();
        
        if (scene.isDirty)
        {
            if (!EditorSceneManager.SaveCurrentModifiedScenesIfUserWantsTo())
            {
                return; // User cancelled the save
            }
        }
        
        // Create a new scene
        scene = EditorSceneManager.NewScene(NewSceneSetup.DefaultGameObjects, NewSceneMode.Single);
        
        // Set up the scene
        SetupSceneHierarchy();
        
        // Save the scene
        string scenePath = "Assets/Scenes/BattleScene.unity";
        EnsureDirectoryExists("Assets/Scenes");
        EditorSceneManager.SaveScene(scene, scenePath);
        
        // Refresh the asset database
        AssetDatabase.Refresh();
        
        Debug.Log("Battle scene setup complete!");
    }
    
    private static void SetupSceneHierarchy()
    {
        // Create the main manager object
        GameObject managers = new GameObject("Managers");
        
        // Add BattleManager
        GameObject battleManagerObj = new GameObject("BattleManager");
        battleManagerObj.AddComponent<BattleManager>();
        battleManagerObj.transform.SetParent(managers.transform);
        
        // Add GameInitializer
        GameObject gameInitializerObj = new GameObject("GameInitializer");
        gameInitializerObj.AddComponent<GameInitializer>();
        gameInitializerObj.transform.SetParent(managers.transform);
        
        // Create UI
        SetupUI(managers);
        
        // Create spawn points
        SetupSpawnPoints();
        
        // Set up lighting
        SetupLighting();
        
        // Set up camera
        SetupCamera();
    }
    
    private static void SetupUI(GameObject managers)
    {
        // Create Canvas
        GameObject canvasObj = new GameObject("BattleCanvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvasObj.AddComponent<UnityEngine.UI.CanvasScaler>();
        canvasObj.AddComponent<UnityEngine.UI.GraphicRaycaster>();
        
        // Add EventSystem if it doesn't exist
        if (Object.FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
        {
            GameObject eventSystem = new GameObject("EventSystem");
            eventSystem.AddComponent<UnityEngine.EventSystems.EventSystem>();
            eventSystem.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
        }
        
        // Create UI Manager
        GameObject uiManagerObj = new GameObject("UIManager");
        uiManagerObj.transform.SetParent(managers.transform);
        
        // Add BattleUI component
        var battleUI = uiManagerObj.AddComponent<TacticalCombatSystem.UI.BattleUI>();

        // Create UI Panels
        // Note: This is a basic setup. The BattleUI script expects specific panels and UI elements
        // to be assigned to its public fields in the inspector. This script does not do that.
        CreateUIPanel(canvasObj.transform, "BattleHUD");
        CreateUIPanel(canvasObj.transform, "CharacterInfo");
        CreateUIPanel(canvasObj.transform, "BattleMenu");
    }
    
    private static void CreateUIPanel(Transform parent, string name)
    {
        GameObject panel = new GameObject(name + "Panel");
        panel.transform.SetParent(parent, false);
        panel.AddComponent<UnityEngine.UI.Image>().color = new Color(0, 0, 0, 0.5f);
        
        // Add layout group
        var layout = panel.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
        layout.childControlWidth = true;
        layout.childControlHeight = true;
        layout.childForceExpandWidth = true;
        layout.childForceExpandHeight = true;
        
        // Add content size fitter
        var fitter = panel.AddComponent<UnityEngine.UI.ContentSizeFitter>();
        fitter.horizontalFit = UnityEngine.UI.ContentSizeFitter.FitMode.PreferredSize;
        fitter.verticalFit = UnityEngine.UI.ContentSizeFitter.FitMode.PreferredSize;
    }
    
    private static void SetupSpawnPoints()
    {
        // Create spawn point container
        GameObject spawnPoints = new GameObject("SpawnPoints");
        
        // Create player spawn points
        GameObject playerSpawns = new GameObject("PlayerSpawns");
        playerSpawns.transform.SetParent(spawnPoints.transform);
        
        // Create enemy spawn points
        GameObject enemySpawns = new GameObject("EnemySpawns");
        enemySpawns.transform.SetParent(spawnPoints.transform);
        
        // Create some default spawn points
        for (int i = 0; i < 4; i++)
        {
            // Player spawns on the left
            GameObject playerSpawn = new GameObject($"PlayerSpawn_{i}");
            playerSpawn.transform.SetParent(playerSpawns.transform);
            playerSpawn.transform.position = new Vector3(-5f + (i * 2), 0, 0);
            
            // Enemy spawns on the right
            GameObject enemySpawn = new GameObject($"EnemySpawn_{i}");
            enemySpawn.transform.SetParent(enemySpawns.transform);
            enemySpawn.transform.position = new Vector3(5f - (i * 2), 0, 0);
        }
    }
    
    private static void SetupLighting()
    {
        // Create directional light
        GameObject lightObj = new GameObject("Directional Light");
        Light light = lightObj.AddComponent<Light>();
        light.type = LightType.Directional;
        light.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
        light.intensity = 1f;
        light.shadows = LightShadows.Soft;
        
        // Set up ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.2f, 0.2f, 0.3f);
        RenderSettings.ambientEquatorColor = new Color(0.5f, 0.5f, 0.5f);
        RenderSettings.ambientGroundColor = new Color(0.3f, 0.3f, 0.4f);
        RenderSettings.ambientIntensity = 1f;
    }
    
    private static void SetupCamera()
    {
        // Find or create main camera
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            cameraObj.tag = "MainCamera";
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.AddComponent<AudioListener>();
        }
        
        // Set up camera position and settings
        mainCamera.transform.position = new Vector3(0, 10, -10);
        mainCamera.transform.rotation = Quaternion.Euler(45, 0, 0);
        mainCamera.orthographic = true;
        mainCamera.orthographicSize = 7f;
        mainCamera.nearClipPlane = 0.3f;
        mainCamera.farClipPlane = 100f;
        mainCamera.clearFlags = CameraClearFlags.Skybox;
        
        // Add Cinemachine components if available
        #if CINEMACHINE_UNITY
        var brain = mainCamera.gameObject.GetComponent<Cinemachine.CinemachineBrain>();
        if (brain == null)
        {
            brain = mainCamera.gameObject.AddComponent<Cinemachine.CinemachineBrain>();
        }
        brain.m_DefaultBlend = new Cinemachine.CinemachineBlendDefinition(Cinemachine.CinemachineBlendDefinition.Style.EaseInOut, 0.5f);
        
        // Create virtual camera
        GameObject vcamObj = new GameObject("CM vcam1");
        var vcam = vcamObj.AddComponent<Cinemachine.CinemachineVirtualCamera>();
        vcam.m_Lens.Orthographic = true;
        vcam.m_Lens.OrthographicSize = 7f;
        vcam.m_Lens.NearClipPlane = 0.3f;
        vcam.m_Lens.FarClipPlane = 100f;
        vcam.Priority = 10;
        
        // Create target group for camera
        GameObject targetGroupObj = new GameObject("CameraTargetGroup");
        var targetGroup = targetGroupObj.AddComponent<Cinemachine.CinemachineTargetGroup>();
        vcam.Follow = targetGroup.transform;
        vcam.LookAt = targetGroup.transform;
        #endif
    }
    
    private static void EnsureDirectoryExists(string path)
    {
        if (!AssetDatabase.IsValidFolder(path))
        {
            string[] folders = path.Split('/');
            string currentPath = "";
            
            foreach (string folder in folders)
            {
                string newPath = currentPath + folder;
                if (!AssetDatabase.IsValidFolder(newPath))
                {
                    string parentFolder = System.IO.Path.GetDirectoryName(newPath);
                    string newFolder = System.IO.Path.GetFileName(newPath);
                    AssetDatabase.CreateFolder(parentFolder, newFolder);
                }
                currentPath = newPath + "/";
            }
        }
    }
}
