using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem
{
    /// <summary>
    /// Manages combo attacks and chain abilities between characters
    /// </summary>
    public class ComboSystem : MonoBehaviour
    {
        [System.Serializable]
        public class ComboRule
        {
            public string comboName;
            public List<string> requiredActionSequence;
            public CombatAction resultingAction;
            public float timeWindow = 5f; // Time in seconds to complete the combo
            public bool requiresDifferentActors = false;
            public bool consumesActions = true;
            public string description;
        }

        [Header("Combo Configuration")]
        [SerializeField] private List<ComboRule> comboRules = new List<ComboRule>();
        
        [Header("Debug")]
        [SerializeField] private bool logComboEvents = true;
        
        // Track combo state
        private class ActiveCombo
        {
            public ComboRule rule;
            public List<CombatAction> actionsPerformed = new List<CombatAction>();
            public List<ICombatParticipant> participants = new List<ICombatParticipant>();
            public float timeRemaining;
            public bool IsComplete => actionsPerformed.Count >= rule.requiredActionSequence.Count;
            public bool IsExpired => timeRemaining <= 0f;
            
            public ActiveCombo(ComboRule rule, float timeWindow)
            {
                this.rule = rule;
                this.timeRemaining = timeWindow;
            }
            
            public bool MatchesNextAction(string actionName)
            {
                if (actionsPerformed.Count >= rule.requiredActionSequence.Count)
                    return false;
                    
                return string.Equals(rule.requiredActionSequence[actionsPerformed.Count], actionName, System.StringComparison.OrdinalIgnoreCase);
            }
        }
        
        private List<ActiveCombo> activeCombos = new List<ActiveCombo>();
        private Dictionary<string, ComboRule> comboLookup = new Dictionary<string, ComboRule>();
        
        // Singleton instance
        private static ComboSystem _instance;
        public static ComboSystem Instance => _instance;

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void InitializeOnLoad()
        {
            // This ensures the singleton is created automatically when the game starts.
            if (_instance == null)
            {
                GameObject singletonObject = new GameObject("ComboSystem");
                _instance = singletonObject.AddComponent<ComboSystem>(); // Assign instance directly
            }
        }
        
        private void Awake()
        {
            // Singleton pattern
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            
            _instance = this;
            DontDestroyOnLoad(gameObject);
            
            // If there are no combo rules, log a warning and skip initialization
            if (comboRules == null || comboRules.Count == 0)
            {
                Debug.LogWarning("ComboSystem initialized with 0 combo rules. Combo functionality will be disabled.");
                return;
            }
            
            // Initialize combo lookup
            foreach (var rule in comboRules)
            {
                if (!string.IsNullOrEmpty(rule.comboName) && rule.requiredActionSequence != null && rule.requiredActionSequence.Count > 0)
                {
                    if (!comboLookup.ContainsKey(rule.comboName))
                    {
                        comboLookup[rule.comboName] = rule;
                    }
                    else if (logComboEvents)
                    {
                        Debug.LogWarning($"Duplicate combo name found: {rule.comboName}");
                    }
                }
            }
            
            if (logComboEvents)
            {
                Debug.LogWarning($"ComboSystem initialized with {comboRules.Count} combo rules");
            }
        }
        
        private void Update()
        {
            // Update active combos
            for (int i = activeCombos.Count - 1; i >= 0; i--)
            {
                var combo = activeCombos[i];
                combo.timeRemaining -= Time.deltaTime;
                
                if (combo.IsExpired)
                {
                    if (logComboEvents)
                    {
                        Debug.Log($"Combo {combo.rule.comboName} expired");
                    }
                    activeCombos.RemoveAt(i);
                }
            }
        }
        
        /// <summary>
        /// Register an action to potentially trigger or continue a combo
        /// </summary>
        public bool RegisterAction(CombatAction action, ICombatParticipant user, ICombatParticipant target)
        {
            if (action == null || user == null) return false;
            
            bool comboTriggered = false;
            string actionName = action.name;
            
            // Check existing active combos first
            for (int i = activeCombos.Count - 1; i >= 0; i--)
            {
                var combo = activeCombos[i];
                
                // Check if this action continues the combo
                if (combo.MatchesNextAction(actionName))
                {
                    // Check if we need different actors and this actor was the last one
                    if (combo.rule.requiresDifferentActors && 
                        combo.participants.Count > 0 && 
                        combo.participants[combo.participants.Count - 1] == user)
                    {
                        continue; // Skip if same actor isn't allowed to chain
                    }
                    
                    // Add to combo
                    combo.actionsPerformed.Add(action);
                    combo.participants.Add(user);
                    combo.timeRemaining = combo.rule.timeWindow; // Reset timer
                    
                    if (logComboEvents)
                    {
                        Debug.Log($"Combo {combo.rule.comboName} progress: {combo.actionsPerformed.Count}/{combo.rule.requiredActionSequence.Count}");
                    }
                    
                    // Check if combo is complete
                    if (combo.IsComplete)
                    {
                        ExecuteCombo(combo, target);
                        activeCombos.RemoveAt(i);
                        comboTriggered = true;
                    }
                    
                    return comboTriggered;
                }
            }
            
            // If no active combo matched, check if this starts a new combo
            foreach (var rule in comboRules)
            {
                if (rule.requiredActionSequence.Count > 0 && 
                    string.Equals(rule.requiredActionSequence[0], actionName, System.StringComparison.OrdinalIgnoreCase))
                {
                    // Start a new combo
                    var newCombo = new ActiveCombo(rule, rule.timeWindow);
                    newCombo.actionsPerformed.Add(action);
                    newCombo.participants.Add(user);
                    activeCombos.Add(newCombo);
                    
                    if (logComboEvents)
                    {
                        Debug.Log($"Started new combo: {rule.comboName} (1/{rule.requiredActionSequence.Count})");
                    }
                    
                    // If it's a 1-action combo, execute it immediately
                    if (rule.requiredActionSequence.Count == 1)
                    {
                        ExecuteCombo(newCombo, target);
                        activeCombos.Remove(newCombo);
                        return true;
                    }
                    
                    return false;
                }
            }
            
            return comboTriggered;
        }
        
        private void ExecuteCombo(ActiveCombo combo, ICombatParticipant target)
        {
            if (combo.rule.resultingAction == null)
            {
                Debug.LogWarning($"Combo {combo.rule.comboName} has no resulting action!");
                return;
            }
            
            if (logComboEvents)
            {
                Debug.Log($"Executing combo: {combo.rule.comboName}");
            }
            
            // Get the last participant in the combo chain
            var lastParticipant = combo.participants.Count > 0 ? combo.participants[combo.participants.Count - 1] : null;
            
            if (lastParticipant != null)
            {
                // Execute the resulting action using the ComboSystem's coroutine capability
                StartCoroutine(combo.rule.resultingAction.ExecuteCoroutine(lastParticipant, target, null));

                // Consume the original actions if needed
                if (combo.rule.consumesActions)
                {
                    for (int i = 0; i < combo.actionsPerformed.Count; i++)
                    {
                        var action = combo.actionsPerformed[i];
                        var participant = combo.participants[i];
                        participant.OnActionPerformed(action);
                    }
                }
            }
            
            // Notify any listeners
            OnComboExecuted?.Invoke(combo.rule, lastParticipant, target);
        }
        
        /// <summary>
        /// Add a combo rule at runtime
        /// </summary>
        public void AddComboRule(ComboRule rule)
        {
            if (rule != null && !string.IsNullOrEmpty(rule.comboName) && 
                rule.requiredActionSequence != null && rule.requiredActionSequence.Count > 0)
            {
                comboRules.Add(rule);
                
                if (!comboLookup.ContainsKey(rule.comboName))
                {
                    comboLookup[rule.comboName] = rule;
                }
                
                if (logComboEvents)
                {
                    Debug.Log($"Added combo rule: {rule.comboName}");
                }
            }
        }
        
        /// <summary>
        /// Remove a combo rule by name
        /// </summary>
        public bool RemoveComboRule(string comboName)
        {
            if (string.IsNullOrEmpty(comboName)) return false;
            
            var rule = comboRules.Find(r => r.comboName == comboName);
            if (rule != null)
            {
                comboRules.Remove(rule);
                comboLookup.Remove(comboName);
                
                // Remove any active combos using this rule
                activeCombos.RemoveAll(c => c.rule == rule);
                
                if (logComboEvents)
                {
                    Debug.Log($"Removed combo rule: {comboName}");
                }
                
                return true;
            }
            
            return false;
        }
        
        // Events
        public delegate void ComboEventHandler(ComboRule rule, ICombatParticipant user, ICombatParticipant target);
        public static event ComboEventHandler OnComboExecuted;
    }
}
