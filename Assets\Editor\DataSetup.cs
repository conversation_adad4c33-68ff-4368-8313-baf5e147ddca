using UnityEditor;
using UnityEngine;
using TacticalCombatSystem.Characters;
using System;

public class DataSetup : EditorWindow
{
    [MenuItem("Tactical Combat/Setup Initial Data")]
    public static void SetupInitialData()
    {
        // Create the data directory if it doesn't exist
        string dataPath = "Assets/Data/";
        if (!AssetDatabase.IsValidFolder(dataPath))
        {
            AssetDatabase.CreateFolder("Assets", "Data");
        }
        if (!AssetDatabase.IsValidFolder(dataPath + "Characters"))
        {
            AssetDatabase.CreateFolder(dataPath, "Characters");
        }
        
        // Create characters
        Create<PERSON>haracterAsset("Warrior", "A strong melee fighter", 150, 30, 20, 15, 10, 100);
        CreateCharacterAsset("Mage", "A powerful spellcaster", 80, 10, 10, 30, 20, 200);
        CreateCharacterAsset("Archer", "A nimble ranged attacker", 100, 25, 15, 20, 25, 120);
        CreateCharacterAsset("Healer", "A supportive healer", 90, 15, 15, 25, 15, 150);
        
        // Refresh the asset database
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        Debug.Log("Initial character data setup complete!");
    }
    
    private static void CreateCharacterAsset(string name, string description, int hp, int atk, int def, int matk, int mdef, int spd)
    {
        // Create a new character asset
        Character character = ScriptableObject.CreateInstance<Character>();
        
        // Use SerializedObject to modify properties
        SerializedObject so = new SerializedObject(character);
        so.FindProperty("characterName").stringValue = name;
        so.FindProperty("description").stringValue = description;
        so.FindProperty("maxHealth").intValue = hp;
        so.FindProperty("attack").intValue = atk;
        so.FindProperty("defense").intValue = def;
        so.FindProperty("magicAttack").intValue = matk;
        so.FindProperty("magicDefense").intValue = mdef;
        so.FindProperty("speed").intValue = spd;
        so.FindProperty("criticalChance").floatValue = 0.1f;
        so.FindProperty("criticalMultiplier").floatValue = 1.5f;
        so.ApplyModifiedProperties();

        character.Initialize();
        
        // Save the character asset
        string path = $"Assets/Data/Characters/{name}.asset";
        AssetDatabase.CreateAsset(character, path);
        AssetDatabase.SaveAssets();
    }
}
