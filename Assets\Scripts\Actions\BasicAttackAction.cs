using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Actions
{
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Basic Attack")]
    public class BasicAttackAction : CombatAction
    {
        [Header("Basic Attack Settings")]
        public int baseDamage = 10;
        public DamageType damageType = DamageType.Physical;
        public bool canCrit = true;
        [Range(0f, 1f)] public float critChance = 0.1f;
        public float critMultiplier = 1.5f;

        [Header("Animation")]
        [Tooltip("The name of the animation trigger to play when this action is executed.")]
        [SerializeField] private string animationTrigger = "Attack";
        public override string AnimationTriggerName => animationTrigger;
        
        public override TargetType TargetType => TargetType.SingleEnemy;
        
        private void OnEnable()
        {
            // Set up default values
            actionName = "Basic Attack";
            description = "A basic attack that deals physical damage.";
            validTargets = TargetTeam.Enemy;
            range = 1;
            mpCost = 0;
        }
        
        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            Debug.Log($"{user.ParticipantName} attacks {target.ParticipantName} with {actionName}!");

            // Pay costs
            user.CurrentMP -= mpCost;
            user.CurrentHP = Mathf.Max(1, user.CurrentHP - hpCost);
            
            // Calculate damage
            bool isCrit = canCrit && Random.value <= critChance;
            float damageMultiplier = isCrit ? critMultiplier : 1f;
            
            // Calculate base damage
            float damage = baseDamage;
            
            // Apply user's attack stat and target's defense
            switch (damageType)
            {
                case DamageType.Physical:
                    damage += user.Attack;
                    damage *= 100f / (100f + target.Defense);
                    break;
                    
                case DamageType.Magical:
                    damage += user.MagicAttack;
                    damage *= 100f / (100f + target.MagicDefense);
                    break;
                    
                case DamageType.True:
                    // Bypasses defenses
                    break;
            }
            
            // Apply crit multiplier
            if (isCrit)
            {
                damage *= damageMultiplier;
                Debug.Log($"{user.ParticipantName}'s attack was a critical hit!");
            }
            
            // Apply Defend bonus
            if (target.IsDefending)
            {
                damage *= 0.5f; // 50% damage reduction
                Debug.Log($"{target.ParticipantName} is defending! Damage reduced.");
            }
            
            // Apply final damage (rounded to nearest int)
            int finalDamage = Mathf.RoundToInt(damage);
            
            // Apply damage to target
            target.CurrentHP -= finalDamage;
            
            // Notify target
            target.OnDamageTaken(finalDamage, user);
            
            // Apply any status effects
            foreach (var effect in statusEffects)
            {
                if (effect != null)
                {
                    target.ApplyStatusEffect(Instantiate(effect));
                }
            }
            
            // Apply cooldown
            ApplyCooldown();
            
            // Log the action
            Debug.Log($"{user.ParticipantName} uses {actionName} on {target.ParticipantName} for {finalDamage} damage!");
        }
        
        public override void OnSelected(ICombatParticipant user)
        {
            base.OnSelected(user);
            Debug.Log($"{actionName} selected by {user.ParticipantName}");
        }
        
        public override void OnDeselected(ICombatParticipant user)
        {
            base.OnDeselected(user);
            Debug.Log($"{actionName} deselected by {user.ParticipantName}");
        }
    }
    
    public enum DamageType
    {
        Physical,
        Magical,
        True
    }
}
