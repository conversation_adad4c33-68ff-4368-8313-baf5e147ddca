using UnityEngine;
using System;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;

namespace TacticalCombatSystem.Core
{
    public enum StatusType
    {
        <PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON><PERSON>,
        <PERSON>,
        <PERSON>ze,
        Sleep,
        Silence,
        StatModifier,
        Custom
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Status Effect")]
    public class StatusEffect : ScriptableObject, IStatusEffect
    {
        [Header("Basic Info")]
        public string statusName = "New Status";
        [TextArea] public string description = "Status effect description";
        public StatusType statusType = StatusType.Debuff;
        public Sprite icon;
        public Color statusColor = Color.white;
        
        [Header("Duration")]
        public int duration = 3; // Number of turns
        public bool isPermanent = false;
        public bool removeOnCombatEnd = true;
        
        [Header("Visuals")]
        public GameObject visualEffectPrefab;
        public bool showFloatingText = true;
        public string floatingText = "!";

        // IStatusEffect implementation
        public string Name => statusName;
        public string Description => description;
        public Sprite Icon => icon;
        public int Duration { get => currentDuration; set => currentDuration = value; }

        [Header("Stat Modifiers")]
        public List<StatModifier> statModifiers = new List<StatModifier>();

        // Stat Modifiers
        // Note: StatModifier will need to be defined or replaced with a simpler approach

        // Events
        public event Action<ICombatParticipant> OnApplied;
        public event Action<ICombatParticipant> OnRemoved;
        public event Action<ICombatParticipant> OnTurnStartEvent;
        public event Action<ICombatParticipant> OnTurnEndEvent;

        // Current state (not serialized)
        [System.NonSerialized] private int currentDuration;
        [System.NonSerialized] private ICombatParticipant target;
        [System.NonSerialized] private GameObject activeVisualEffect;

        // For custom behavior
        public Action<ICombatParticipant> OnApplyAction;
        public Action<ICombatParticipant> OnRemoveAction;
        public Action<ICombatParticipant> OnTurnStartCustom;
        public Action<ICombatParticipant> OnTurnEndCustom;
        
        /// <summary>
        /// Initialize the status effect on a target character
        /// </summary>
        public void Apply(ICombatParticipant target)
        {
            OnApply(target);
        }

        public void OnApply(ICombatParticipant target) {
            Initialize(target);
        }

        public void Remove(ICombatParticipant target)
        {
            OnRemove(target);
        }

        public void OnRemove(ICombatParticipant target) {
            RemoveEffect();
        }

        public void OnTurnStart(ICombatParticipant target) {
            OnTurnStartEffect();
        }

        public void OnTurnEnd(ICombatParticipant target) {
            OnTurnEndEffect();
        }

        public IStatusEffect Clone() {
            return Instantiate(this);
        }

        public void Initialize(ICombatParticipant targetCharacter, int customDuration = -1)
        {
            if (targetCharacter == null) return;
            
            target = targetCharacter;
            currentDuration = customDuration >= 0 ? customDuration : duration;
            
            // Apply the effect
            ApplyEffect();
            
            // Create visual effect if needed
            if (visualEffectPrefab != null && targetCharacter is MonoBehaviour mb)
            {
                activeVisualEffect = Instantiate(visualEffectPrefab, mb.transform);
                activeVisualEffect.transform.localPosition = Vector3.zero;
            }

            // Show floating text
            if (showFloatingText && !string.IsNullOrEmpty(floatingText))
            {
                // TODO: Show floating text above character
                Debug.Log($"{floatingText} - {targetCharacter.ParticipantName}");
            }

            // Invoke events
            OnApplied?.Invoke(targetCharacter);
            OnApplyAction?.Invoke(targetCharacter);

            Debug.Log($"{statusName} applied to {targetCharacter.ParticipantName} for {currentDuration} turns");
        }
        
        /// <summary>
        /// Called at the start of the target's turn
        /// </summary>
        public void OnTurnStartEffect()
        {
            if (target == null || !target.IsAlive) return;
            
            // Invoke custom turn start behavior
            OnTurnStartEvent?.Invoke(target);
            OnTurnStartCustom?.Invoke(target);
            
            // Apply damage over time effects
            if (statusType == StatusType.Poison || statusType == StatusType.Burn)
            {
                int damage = Mathf.RoundToInt(target.MaxHP * 0.05f); // 5% max HP per turn
                target.OnDamageTaken(damage, target); // Simplified damage application

                Debug.Log($"{target.ParticipantName} takes {damage} damage from {statusName}!");
            }

            // Handle stun/sleep effects
            if ((statusType == StatusType.Stun || statusType == StatusType.Sleep) && target is MonoBehaviour)
            {
                // Note: SkipNextTurn functionality would need to be implemented in the character class
                Debug.Log($"{target.ParticipantName} is {statusName.ToLower()}ed and skips their turn!");
            }
        }
        
        public void OnTurnEndEffect()
        {
            if (target == null || !target.IsAlive) return;
            
            // Invoke custom turn end behavior
            OnTurnEndEvent?.Invoke(target);
            OnTurnEndCustom?.Invoke(target);
            
            // Reduce duration if not permanent
            if (!isPermanent)
            {
                currentDuration--;
                
                // Remove if duration expired
                if (currentDuration <= 0)
                {
                    RemoveEffect();
                }
            }
        }
        
        public void Refresh(int newDuration = -1)
        {
            if (newDuration > 0)
            {
                currentDuration = newDuration;
            }
            else if (!isPermanent)
            {
                currentDuration = Mathf.Max(currentDuration, duration);
            }
            
            Debug.Log($"{statusName} on {target.ParticipantName} refreshed to {currentDuration} turns");
        }
        
        public void RemoveEffect()
        {
            if (target == null) return;
            
            // Clean up visual effect
            if (activeVisualEffect != null)
            {
                Destroy(activeVisualEffect);
            }
            
            // Remove from target
            if (target != null)
            {
                target.RemoveStatusEffect(this);
                
                // Invoke removal events
                OnRemoved?.Invoke(target);
                OnRemoveAction?.Invoke(target);
                
                Debug.Log($"{statusName} removed from {target.ParticipantName}");
            }
            
            // If this is a runtime-created status effect, destroy it
            if (!string.IsNullOrEmpty(name) && name.StartsWith("New Status"))
            {
                Destroy(this);
            }
        }
        
        protected virtual void ApplyEffect()
        {
            // Base implementation does nothing
            // Override in derived classes for specific effects
        }
        
        /// <summary>
        /// Creates a new status effect that modifies a stat.
        /// </summary>
        public static StatusEffect CreateStatModifier(string name, string stat, int value, int duration, bool isBuff = true)
        {
            StatusEffect effect = CreateInstance<StatusEffect>();
            effect.statusName = name;
            effect.statusType = StatusType.StatModifier;
            effect.duration = duration;
            effect.isPermanent = duration <= 0;
            effect.statusColor = isBuff ? new Color(0.2f, 0.8f, 0.2f) : new Color(0.8f, 0.2f, 0.2f);
            effect.statModifiers.Add(new StatModifier { stat = stat, value = value });
            return effect;
        }

        // Helper method to create a simple status effect
        public static StatusEffect CreateSimpleEffect(
            string name,
            StatusType statusType,
            int duration,
            bool isBuff = true)
        {
            StatusEffect effect = CreateInstance<StatusEffect>();
            effect.statusName = name;
            effect.statusType = statusType;
            effect.duration = duration;
            effect.isPermanent = duration <= 0;
            effect.statusColor = isBuff ? new Color(0.2f, 0.8f, 0.2f) : new Color(0.8f, 0.2f, 0.2f);

            return effect;
        }
    }

    [System.Serializable]
    public class StatModifier
    {
        public string stat; // e.g., "Attack", "Defense"
        public int value;
        public bool isPercentage = false;
    }
}
