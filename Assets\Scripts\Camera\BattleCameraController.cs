using Cinemachine;
using TacticalCombatSystem.Interfaces;
using UnityEngine;

namespace TacticalCombatSystem.Camera
{
    public class BattleCameraController : MonoBehaviour
    {
        public static BattleCameraController Instance { get; private set; }

        [Header("Virtual Cameras")]
        [SerializeField] private CinemachineVirtualCamera wideShotCamera;
        [SerializeField] private CinemachineVirtualCamera playerTurnCamera;
        [SerializeField] private CinemachineVirtualCamera enemyTurnCamera;
        [SerializeField] private CinemachineVirtualCamera actionCamera;

        private void Awake()
        {
            if (Instance != null && Instance != this)
            {
                Destroy(gameObject);
            }
            else
            {
                Instance = this;
            }
        }

        private void Start()
        {
            // Start with a wide shot of the battlefield
            SwitchToWideShot();
        }

        public void SwitchToWideShot()
        {
            SetCameraPriority(wideShotCamera);
        }

        public void SwitchToPlayerTurn(ICombatParticipant player)
        {
            playerTurnCamera.Follow = player.GetTransform();
            playerTurnCamera.LookAt = player.GetTransform();
            SetCameraPriority(playerTurnCamera);
        }

        public void SwitchToEnemyTurn(ICombatParticipant enemy)
        {
            enemyTurnCamera.Follow = enemy.GetTransform();
            enemyTurnCamera.LookAt = enemy.GetTransform();
            SetCameraPriority(enemyTurnCamera);
        }

        public void SwitchToActionCamera(ICombatParticipant user, ICombatParticipant target)
        {
            actionCamera.Follow = user.GetTransform();
            actionCamera.LookAt = target.GetTransform();
            SetCameraPriority(actionCamera);
        }

        private void SetCameraPriority(CinemachineVirtualCamera activeCamera)
        {
            // Reset all camera priorities to 0
            if (wideShotCamera != null) wideShotCamera.Priority = 0;
            if (playerTurnCamera != null) playerTurnCamera.Priority = 0;
            if (enemyTurnCamera != null) enemyTurnCamera.Priority = 0;
            if (actionCamera != null) actionCamera.Priority = 0;

            // Set the active camera's priority to 10
            if (activeCamera != null)
            {
                activeCamera.Priority = 10;
            }
        }
    }
}
