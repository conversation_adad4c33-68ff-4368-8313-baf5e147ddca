using System.Collections.Generic;
using System.Linq;
using TacticalCombatSystem.Interfaces;
using UnityEngine;

namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Manages keyboard/controller-driven targeting during battle.
    /// This class no longer handles direct input, but is controlled by BattleUI.
    /// It manages the state of target selection and visual feedback (highlighting).
    /// </summary>
    public class BattleInputHandler : MonoBehaviour
    {
        [Header("Selection Visuals")]
        public Color targetColor = new Color(1f, 0.5f, 0f); // Color for the currently cycled target

        private List<ICombatParticipant> _validTargets;
        private ICombatParticipant _currentTarget;
        private int _currentTargetIndex = -1;

        public bool IsTargeting { get; private set; }

        /// <summary>
        /// Begins the targeting mode, providing a list of valid targets.
        /// </summary>
        /// <param name="targets">A list of participants that can be targeted.</param>
        public void BeginTargetSelection(List<ICombatParticipant> targets)
        {
            _validTargets = targets.Where(t => t.IsAlive).ToList();
            if (_validTargets.Count == 0) return;

            IsTargeting = true;
            _currentTargetIndex = 0;
            UpdateTargetHighlight();
        }

        /// <summary>
        /// Ends the targeting mode and clears highlights.
        /// </summary>
        public void EndTargetSelection()
        {
            if (_currentTarget != null)
            {
                _currentTarget.ResetHighlight();
            }

            IsTargeting = false;
            _currentTarget = null;
            _currentTargetIndex = -1;
            _validTargets = null;
        }

        /// <summary>
        /// Cycles to the next or previous target in the list.
        /// </summary>
        /// <param name="direction">+1 for next, -1 for previous.</param>
        public void CycleTarget(int direction)
        {
            if (!IsTargeting || _validTargets.Count == 0) return;

            _currentTargetIndex += direction;

            // Wrap around the index
            if (_currentTargetIndex < 0) _currentTargetIndex = _validTargets.Count - 1;
            if (_currentTargetIndex >= _validTargets.Count) _currentTargetIndex = 0;

            UpdateTargetHighlight();
        }

        /// <summary>
        /// Gets the currently selected combat participant.
        /// </summary>
        /// <returns>The current target, or null if not targeting.</returns>
        public ICombatParticipant GetCurrentTarget()
        {
            return _currentTarget;
        }

        private void UpdateTargetHighlight()
        {
            // Reset the highlight on the previous target
            if (_currentTarget != null)
            {
                _currentTarget.ResetHighlight();
            }

            // Set the new current target
            _currentTarget = _validTargets[_currentTargetIndex];

            // Highlight the new target
            if (_currentTarget != null)
            {
                _currentTarget.Highlight(targetColor);
            }
        }
        
        private void OnDisable()
        {
            // Ensure targeting mode is ended if the object is disabled
            if (IsTargeting)
            {
                EndTargetSelection();
            }
        }
    }
}
