using UnityEngine;
using UnityEngine.UI;
using TMPro;
using TacticalCombatSystem.Core;
using System.Collections.Generic;

namespace TacticalCombatSystem.Characters
{
    [RequireComponent(typeof(BaseCharacter))]
    public class CharacterVisual : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Image healthBar;
        [SerializeField] private Image manaBar;
        [SerializeField] private TextMeshProUGUI nameText;
        [SerializeField] private TextMeshProUGUI healthText;

        public BaseCharacter BaseCharacter { get; private set; }

        private void Awake()
        {
            BaseCharacter = GetComponent<BaseCharacter>();
        }

        private void OnEnable()
        {
            if (BaseCharacter != null)
            {
                BaseCharacter.OnHealthChanged += OnHealthChanged;
                BaseCharacter.OnManaChanged += OnManaChanged;
                BaseCharacter.OnStatusEffectAdded += OnStatusEffectAdded;
                BaseCharacter.OnStatusEffectRemoved += OnStatusEffectRemoved;
                Initialize();
            }
        }

        private void OnDisable()
        {
            if (BaseCharacter != null)
            {
                BaseCharacter.OnHealthChanged -= OnHealthChanged;
                BaseCharacter.OnManaChanged -= OnManaChanged;
                BaseCharacter.OnStatusEffectAdded -= OnStatusEffectAdded;
                BaseCharacter.OnStatusEffectRemoved -= OnStatusEffectRemoved;
            }
        }

        private void Initialize()
        {
            if (BaseCharacter.CharacterData == null) return;

            if (nameText != null) nameText.text = BaseCharacter.CharacterData.CharacterName;
            UpdateHealthUI();
            UpdateManaUI();
            UpdateStatusEffects();
        }

        private void UpdateHealthUI()
        {
            if (BaseCharacter.CharacterData == null) return;

            if (healthBar != null)
            {
                healthBar.fillAmount = (float)BaseCharacter.CurrentHP / BaseCharacter.MaxHP;
            }

            if (healthText != null)
            {
                healthText.text = $"{BaseCharacter.CurrentHP} / {BaseCharacter.MaxHP}";
            }
        }

        private void UpdateManaUI()
        {
            if (BaseCharacter.CharacterData == null) return;

            if (manaBar != null)
            {
                manaBar.fillAmount = (float)BaseCharacter.CurrentMP / BaseCharacter.MaxMP;
            }
        }

        private void UpdateStatusEffects()
        {
            // This functionality is temporarily disabled until status effects are fully implemented.
            // Debug.Log("Updating status effects UI...");
        }

        #region Event Handlers

        private void OnHealthChanged(int currentHealth, int maxHealth)
        {
            UpdateHealthUI();
        }

        private void OnManaChanged(int currentMana, int maxMana)
        {
            UpdateManaUI();
        }

        private void OnStatusEffectAdded(object effect)
        {
            UpdateStatusEffects();
        }

        private void OnStatusEffectRemoved(object effect)
        {
            UpdateStatusEffects();
        }

        #endregion
    }
}
