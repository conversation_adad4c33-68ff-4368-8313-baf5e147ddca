namespace TacticalCombatSystem.Core
{
    /// <summary>
    /// Defines the category of a combat action
    /// </summary>
    public enum ActionCategory
    {
        /// <summary>
        /// Basic attack with no resource cost
        /// </summary>
        Basic,
        
        /// <summary>
        /// Physical abilities that cost Energy
        /// </summary>
        Physical,
        
        /// <summary>
        /// Magical spells that cost Ether
        /// </summary>
        Magical,
        
        /// <summary>
        /// Defensive actions
        /// </summary>
        Defend,
        
        /// <summary>
        /// Items from inventory
        /// </summary>
        Item,
        
        /// <summary>
        /// Special actions like Run
        /// </summary>
        Special
    }
    
    /// <summary>
    /// Type of resource required to use an action
    /// </summary>
    public enum ResourceType
    {
        None,
        Energy,
        Ether,
        Item
    }
}
