using UnityEngine;
using System.Collections.Generic;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Characters;

namespace TacticalCombatSystem.Core
{
    public class GameInitializer : MonoBehaviour
    {
        [Header("Character Data")]
        public List<BaseCharacter> testPlayerTeam = new List<BaseCharacter>();
        public List<BaseCharacter> testEnemyTeam = new List<BaseCharacter>();

        [Header("UI References")]
        public IBattleUI battleUI;
        
        private void Start()
        {
            // Find the BattleManager in the scene
            var battleManager = FindFirstObjectByType<BattleManager>(FindObjectsInactive.Include) as IBattleManager;
            if (battleManager == null)
            {
                Debug.LogError("No BattleManager found in the scene!");
                return;
            }

            // Set up teams
            foreach (var character in testPlayerTeam)
            {
                if (character != null)
                {
                    battleManager.AddToPlayerTeam(character);
                }
            }

            foreach (var character in testEnemyTeam)
            {
                if (character != null)
                {
                    battleManager.AddToEnemyTeam(character);
                }
            }

            // Initialize UI if available
            if (battleUI != null)
            {
                Debug.Log("BattleUI will be initialized through events when battle starts");
            }

            // Start the battle
            battleManager.StartBattle();
        }
    }
}
