using UnityEngine;
using TacticalCombatSystem.Core;
using ICombatParticipant = TacticalCombatSystem.Interfaces.ICombatParticipant;

namespace TacticalCombatSystem.Abilities
{
    public abstract class AbilityEffect : ScriptableObject
    {
        [Header("Effect Settings")]
        public string effectName = "New Effect";
        [TextArea] public string description = "Effect description";
        public bool canMiss = false;
        [Range(0f, 1f)] public float hitChance = 1f;
        
        // Called when the effect is applied
        public abstract void Apply(ICombatParticipant user, ICombatParticipant target);

        // Called when the effect is removed/expires
        public virtual void Remove(ICombatParticipant target) {}

        // Calculate if the effect hits based on hit chance and other factors
        protected bool CheckHit(ICombatParticipant user, ICombatParticipant target)
        {
            if (!canMiss) return true;

            // Simple hit calculation - can be expanded with accuracy/evasion stats
            float hitRoll = Random.value;
            float finalHitChance = hitChance; // Simplified for now since accuracy/evasion aren't in ICombatParticipant

            return hitRoll <= finalHitChance;
        }
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Effects/Damage")]
    public class DamageEffect : AbilityEffect
    {
        public int baseDamage = 10;
        public DamageType damageType = DamageType.Physical;
        public bool canCrit = true;
        [Range(0f, 1f)] public float critChance = 0.1f;
        public float critMultiplier = 1.5f;
        
        public override void Apply(ICombatParticipant user, ICombatParticipant target)
        {
            if (!CheckHit(user, target))
            {
                Debug.Log($"{target.ParticipantName} evaded the attack!");
                return;
            }

            bool isCrit = canCrit && Random.value <= critChance;
            float damageMultiplier = isCrit ? critMultiplier : 1f;

            // Calculate base damage
            float damage = baseDamage;

            // Apply user's attack stat and target's defense
            switch (damageType)
            {
                case DamageType.Physical:
                    damage += user.Attack;
                    damage *= 100f / (100f + target.Defense);
                    break;

                case DamageType.Magical:
                    damage += user.MagicAttack;
                    damage *= 100f / (100f + target.MagicDefense);
                    break;

                case DamageType.True:
                    // Bypasses defenses
                    break;
            }

            // Apply crit multiplier
            if (isCrit)
            {
                damage *= damageMultiplier;
                Debug.Log($"Critical hit!");
            }

            // Apply final damage (rounded to nearest int)
            int finalDamage = Mathf.RoundToInt(damage);

            // Apply damage to target
            target.OnDamageTaken(finalDamage, user);

            Debug.Log($"{user.ParticipantName} deals {finalDamage} {damageType} damage to {target.ParticipantName}");
        }
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Effects/Heal")]
    public class HealEffect : AbilityEffect
    {
        public int baseHeal = 10;
        public bool isPercentage = false;
        
        public override void Apply(ICombatParticipant user, ICombatParticipant target)
        {
            int healAmount = baseHeal;

            if (isPercentage)
            {
                healAmount = Mathf.RoundToInt(target.MaxHP * (baseHeal / 100f));
            }

            // Cap healing to max health
            int actualHeal = Mathf.Min(healAmount, target.MaxHP - target.CurrentHP);

            if (actualHeal > 0)
            {
                target.OnHealed(actualHeal, user);

                Debug.Log($"{user.ParticipantName} heals {target.ParticipantName} for {actualHeal} HP");
            }
        }
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Effects/Status Effect")]
    public class StatusEffectEffect : AbilityEffect
    {
        public StatusEffect statusEffectPrefab;
        public int duration = 3; // Number of turns
        
        public override void Apply(ICombatParticipant user, ICombatParticipant target)
        {
            if (!CheckHit(user, target)) return;

            // Apply the status effect using the interface method
            target.ApplyStatusEffect(statusEffectPrefab);

            Debug.Log($"{statusEffectPrefab.name} applied to {target.ParticipantName} for {duration} turns!");
        }
    }
    
    [CreateAssetMenu(menuName = "Tactical Combat/Effects/Stat Modifier")]
    public class StatModifierEffect : AbilityEffect
    {
        public TacticalCombatSystem.Core.StatType statToModify;
        public float modifierValue; // Can be positive or negative
        public bool isPercentage = false;
        public int duration = 3; // Number of turns
        
        public override void Apply(ICombatParticipant user, ICombatParticipant target)
        {
            if (!CheckHit(user, target)) return;

            // Create a stat-modifying status effect
            var statModEffect = StatusEffect.CreateStatModifier(
                $"{statToModify} {(modifierValue >= 0 ? "+" : "")}{modifierValue}{(isPercentage ? "%" : "")}",
                statToModify.ToString(),
                (int)modifierValue,
                duration,
                modifierValue >= 0
            );

            // Apply the effect using the interface method
            target.ApplyStatusEffect(statModEffect);
            Debug.Log($"{target.ParticipantName}'s {statToModify} is modified for {duration} turns!");
        }
    }
    
    public enum DamageType
    {
        Physical,
        Magical,
        True
    }
    

}
