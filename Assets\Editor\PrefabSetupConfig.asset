%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9a001a3035cce6b42a6a0949a7e632b8, type: 3}
  m_Name: PrefabSetupConfig
  m_EditorClassIdentifier: Assembly-CSharp-Editor::PrefabSetupConfig
  prefabPath: Assets/Prefabs/Characters
  prefabSavePath: Assets/Prefabs/Characters
  uiPrefabPath: Assets/Prefabs/UI
  materialsPath: Assets/Materials
  prefabNamePrefix: Character_
  defaultCharacterName: NewCharacter
  defaultPrefabName: NewCharacter
  defaultMaterialName: CharacterMaterial
  defaultCharacterColor: {r: 1, g: 1, b: 1, a: 1}
  colliderSize: {x: 1, y: 2, z: 1}
  colliderCenter: {x: 0, y: 1, z: 0}
  defaultHealth: 100
  defaultDamage: 10
  defaultMoveSpeed: 5
  defaultAttackRange: 2
  defaultAttackRate: 1
  defaultMaterial: {fileID: 0}
  characterScale: {x: 1, y: 1, z: 1}
  uiOffset: {x: 0, y: 2, z: 0}
  canvasSize: {x: 200, y: 100}
  canvasPosition: {x: 0, y: 2, z: 0}
  canvasScale: {x: 0.01, y: 0.01, z: 0.01}
  canvasPixelsPerUnit: 1
  canvasReferencePixelsPerUnit: 100
  healthBarFillColor: {r: 1, g: 0, b: 0, a: 1}
  healthBarBackgroundColor: {r: 0.2, g: 0.2, b: 0.2, a: 0.7}
  nameFontSize: 14
  nameTextColor: {r: 1, g: 1, b: 1, a: 1}
  damageNumberColor: {r: 1, g: 0.2, b: 0.2, a: 1}
  damageNumberFontSize: 24
  damageNumberSize: {x: 100, y: 50}
  requiredComponents:
  - TacticalCombatSystem.CharacterVisual
  - UnityEngine.BoxCollider
  - UnityEngine.Rigidbody
  validatePrefabOnCreate: 1
  showProgressBars: 1
  enableUndo: 1
