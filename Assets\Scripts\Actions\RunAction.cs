using System.Collections;
using UnityEngine;
using TacticalCombatSystem.Core;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Battle;

namespace TacticalCombatSystem.Actions
{
    [CreateAssetMenu(menuName = "Tactical Combat/Actions/Run")]
    public class RunAction : CombatAction
    {
        public override string AnimationTriggerName => "Run";

        public override TargetType TargetType => TargetType.Self;

        private void OnEnable()
        {
            actionName = "Run";
            description = "Attempt to flee from the battle.";
            validTargets = TargetTeam.Self;
            mpCost = 0;
        }

        public override void Execute(ICombatParticipant user, ICombatParticipant target)
        {
            Debug.Log($"{user.ParticipantName} attempts to run away!");

            // The action signals the intent to run; the BattleManager handles the logic.
            BattleManager.Instance.AttemptToRun();
        }
    }
}
