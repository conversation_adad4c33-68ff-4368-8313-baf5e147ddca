%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7016b43233db98a448dfc964833e24e0, type: 3}
  m_Name: BasicAttack
  m_EditorClassIdentifier: Assembly-CSharp::TacticalCombatSystem.Actions.BasicAttackAction
  actionName: Basic Attack
  description: A basic attack that deals physical damage.
  icon: {fileID: 21300000, guid: 2aece322f6bbbbf4591c050db321460c, type: 3}
  targetType: 2
  validTargets: 3
  range: 1
  requiresLineOfSight: 1
  mpCost: 0
  hpCost: 0
  cooldown: 0
  statusEffects: []
  baseDamage: 10
  damageType: 0
  canCrit: 1
  critChance: 0.1
  critMultiplier: 1.5
