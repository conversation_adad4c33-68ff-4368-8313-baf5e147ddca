using System;
using System.Collections.Generic;
using UnityEngine;
using TacticalCombatSystem.Interfaces;
using TacticalCombatSystem.Interfaces.Core;
using TacticalCombatSystem.Battle;
using TacticalCombatSystem.Core;
using System.Linq;

namespace TacticalCombatSystem.Characters
{
    public class BaseCharacter : MonoBehaviour, ICombatParticipant
    {
        #region Events

        public event Action<int, int> OnHealthChanged;
        public event Action<int, int> OnManaChanged;
        public event Action<object> OnStatusEffectAdded;
        public event Action<object> OnStatusEffectRemoved;

        #endregion

        #region Properties

        public Character CharacterData { get; private set; }

        public string ParticipantName => CharacterData?.CharacterName ?? "Unknown";
        public string Name => CharacterData?.CharacterName ?? "Unknown"; 
        public int MaxHP => CharacterData?.MaxHealth ?? 0;
        public int MaxMP => CharacterData?.MaxMana ?? 0;
        public int Attack => CharacterData?.Attack ?? 0;
        public int Defense => CharacterData?.Defense ?? 0;
        public int MagicAttack => CharacterData?.MagicAttack ?? 0;
        public int MagicDefense => CharacterData?.MagicDefense ?? 0;
        public int Speed => CharacterData?.Speed ?? 0;
        public bool IsAlive => CurrentHP > 0;
        public bool IsPlayerControlled => gameObject.CompareTag("Player");
        public bool IsDefending => activeStatusEffects.Any(e => e.Name == "Defending");
        public Sprite Portrait => CharacterData != null ? CharacterData.Portrait : null;

        public int CurrentHP
        {
            get => currentHP;
            set
            {
                int oldHP = currentHP;
                currentHP = Mathf.Clamp(value, 0, MaxHP);
                if (oldHP != currentHP)
                {
                    OnHealthChanged?.Invoke(currentHP, MaxHP);
                }
            }
        }

        public int CurrentMP
        {
            get => currentMP;
            set
            {
                int oldMP = currentMP;
                currentMP = Mathf.Clamp(value, 0, MaxMP);
                if (oldMP != currentMP)
                {
                    OnManaChanged?.Invoke(currentMP, MaxMP);
                }
            }
        }

        public Transform GetTransform() => transform;

        #endregion

        #region Private Fields

        private IBattleManager battleManager;
        private int currentHP;
        private int currentMP;
        private Animator characterAnimator;
        private readonly List<IStatusEffect> activeStatusEffects = new List<IStatusEffect>();

        #endregion

        #region Serialized Fields

        [Header("Character Data")]
        [SerializeField] private Character characterData;

        #endregion

        #region Unity Methods

        private void Awake()
        {
            characterAnimator = GetComponentInChildren<Animator>();
            if (characterData != null)
            {
                InitializeFromCharacterData(characterData);
            }
        }

        #endregion

        #region Initialization

        public void InitializeFromCharacterData(Character data)
        {
            this.CharacterData = data;
            this.characterData = data; // Keep serialized field in sync
            if (CharacterData != null)
            {
                CurrentHP = MaxHP;
                CurrentMP = MaxMP;
            }
        }

        public void InitializeForBattle(IBattleManager manager)
        {
            this.battleManager = manager;

            if (CharacterData == null)
            {
                Debug.LogError($"CharacterData is not assigned for {gameObject.name}. Battle initialization failed.");
            }
        }

        #endregion

        #region ICombatParticipant Implementation

        public void OnTurnStart() { }

        public void OnTurnEnd() { }

        public void OnActionPerformed(ICombatAction action)
        {
            Debug.Log($"{ParticipantName} performed action: {action.ActionName}");
        }

        public void OnDamageTaken(int amount, ICombatParticipant source) { }

        public void OnHealed(int amount, ICombatParticipant source) { }

        public List<ICombatAction> GetAvailableActions()
        {
            var actions = new List<ICombatAction>();

            // Add the default actions for all characters.
            // We create instances because ScriptableObjects should be treated as templates.
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.BasicAttackAction>());
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.DefendAction>());
            actions.Add(ScriptableObject.CreateInstance<TacticalCombatSystem.Actions.RunAction>());

            // Add any special abilities from the character's data.
            if (CharacterData?.Abilities != null)
            {
                actions.AddRange(CharacterData.Abilities.Cast<ICombatAction>());
            }

            return actions;
        }

        public bool CanPerformAction(ICombatAction action)
        {
            return IsAlive && CurrentMP >= action.ManaCost;
        }

        public List<ICombatParticipant> GetValidTargets(ICombatAction action)
        {
            if (battleManager == null) return new List<ICombatParticipant>();
            return battleManager.GetAllParticipants().Where(p => p.IsAlive && action.IsValidTarget(this, p)).ToList();
        }

        public void ApplyStatusEffect(IStatusEffect effect)
        {
            // Prevent duplicate status effects if needed (optional)
            if (activeStatusEffects.Any(se => se.Name == effect.Name)) return;

            activeStatusEffects.Add(effect);
            effect.OnApply(this);
            battleManager?.ReportStatusEffect(this, effect as StatusEffect);
            OnStatusEffectAdded?.Invoke(effect);
            Debug.Log($"{ParticipantName} is now affected by {effect.Name}");
        }

        public void RemoveStatusEffect(IStatusEffect effect)
        {
            var effectToRemove = activeStatusEffects.FirstOrDefault(se => se.Name == effect.Name);
            if (effectToRemove != null)
            {
                activeStatusEffects.Remove(effectToRemove);
                effectToRemove.OnRemove(this);
                OnStatusEffectRemoved?.Invoke(effectToRemove);
                Debug.Log($"{ParticipantName} is no longer affected by {effectToRemove.Name}");
            }
        }

        public bool HasStatusEffect(string effectName)
        {
            return activeStatusEffects.Any(e => e.Name == effectName);
        }

        public void TriggerAnimation(string triggerName)
        {
            if (characterAnimator != null && characterAnimator.runtimeAnimatorController != null)
            {
                characterAnimator.SetTrigger(triggerName);
            }
            else
            {
                Debug.LogWarning($"Animator not set or missing controller on {gameObject.name}");
            }
        }

        public void Highlight(Color color)
        {
            // Placeholder for visual highlighting logic (e.g., using the provided color)
            Debug.Log($"{ParticipantName} highlighted with color {color}.");
        }

        public void ResetHighlight()
        {
            // Placeholder for removing visual highlighting
            Debug.Log($"{ParticipantName} highlight removed.");
        }

        #endregion

        #region Public Combat Methods

        public void TakeDamage(int amount, ICombatParticipant source)
        {
            if (!IsAlive) return;

            int damageTaken = Mathf.Max(1, amount - Defense);
            CurrentHP -= damageTaken;

            Debug.Log($"{ParticipantName} took {damageTaken} damage!");
            battleManager?.ReportDamage(this, damageTaken);
            OnDamageTaken(damageTaken, source);

            if (!IsAlive)
            {
                HandleDeath();
            }
        }

        public void Heal(int amount)
        {
            if (!IsAlive) return;
            int healAmount = Mathf.Min(amount, MaxHP - CurrentHP);
            CurrentHP += healAmount;
            battleManager?.ReportHeal(this, healAmount);
            OnHealed(healAmount, this);
            Debug.Log($"{ParticipantName} healed for {healAmount} HP!");
        }

        #endregion

        #region Private Methods

        private void HandleDeath()
        {
            Debug.Log($"{ParticipantName} has been defeated!");
            characterAnimator?.SetTrigger("Die");
            battleManager?.RemoveFromBattle(this);
        }

        #endregion
    }
}
